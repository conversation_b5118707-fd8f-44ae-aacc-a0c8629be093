package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.configuration.ConfigurationItem
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton

/**
 * 单个配置项的详细配置界面
 *
 * 显示单个配置项的详细配置内容，右上角有确认按钮
 *
 * @param configurationItem 配置项
 * @param initialConfigData 初始配置数据（编辑模式使用）
 * @param editIndex 编辑项索引（编辑模式使用）
 * @param onConfigurationComplete 配置完成回调
 * @param onNavigateBack 返回回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SingleConfigurationScreen(
    configurationItem: ConfigurationItem,
    initialConfigData: String? = null,
    editIndex: Int? = null,
    onConfigurationComplete: (Any) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    
    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    
    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用自定义字体样式
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.screenTitleFontWeight) {
                "light" -> FontWeight.Light
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "semibold" -> FontWeight.SemiBold
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }
    
    // 配置完成状态
    var isConfigurationComplete by rememberSaveable { mutableStateOf(false) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = configurationItem.title,
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    // 返回按钮 - 主题感知
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 海洋蓝主题：保持原有样式
                        IconButton(onClick = onNavigateBack) {
                            Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                        }
                    }
                },
                actions = {
                    // 确认按钮
                    TextButton(
                        onClick = {
                            isConfigurationComplete = true
                        },
                        enabled = !isConfigurationComplete
                    ) {
                        Text(
                            text = if (isConfigurationComplete) "已确认" else "确认",
                            color = if (isConfigurationComplete) {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            } else {
                                MaterialTheme.colorScheme.primary
                            }
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // 配置项描述
            Text(
                text = configurationItem.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 配置内容
            configurationItem.configComposable(
                initialConfigData,
                { result ->
                    // 配置完成回调
                    onConfigurationComplete(result)
                },
                onNavigateBack
            )
        }
    }
}
