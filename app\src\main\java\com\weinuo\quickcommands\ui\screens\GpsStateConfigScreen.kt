package com.weinuo.quickcommands.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.DeviceEventCondition
import com.weinuo.quickcommands.model.DeviceEventType
import com.weinuo.quickcommands.model.GpsStateType
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import java.util.UUID

/**
 * GPS状态配置界面
 *
 * 专门用于配置GPS状态条件的界面，采用新的单页面配置模式
 *
 * @param onConfigurationComplete 配置完成回调
 * @param onNavigateBack 返回回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GpsStateConfigScreen(
    onConfigurationComplete: (Any) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    
    // 获取主题管理器和设置仓库
    val themeManager = remember { ThemeManager.getInstance(context) }
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()
    
    // 主题感知的标题样式
    val themeAwareTitleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用自定义字体样式
        MaterialTheme.typography.titleLarge.copy(
            fontWeight = when (globalSettings.screenTitleFontWeight) {
                "light" -> FontWeight.Light
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "semibold" -> FontWeight.SemiBold
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            },
            fontSize = globalSettings.screenTitleFontSize.sp
        )
    } else {
        // 海洋蓝主题：保持原有样式
        MaterialTheme.typography.titleLarge
    }
    
    // GPS状态选择
    var selectedGpsStateType by rememberSaveable { mutableStateOf(GpsStateType.ENABLED) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "GPS状态配置",
                        style = themeAwareTitleStyle
                    )
                },
                navigationIcon = {
                    // 返回按钮 - 主题感知
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 海洋蓝主题：保持原有样式
                        IconButton(onClick = onNavigateBack) {
                            Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                        }
                    }
                },
                actions = {
                    // 确认按钮
                    TextButton(
                        onClick = {
                            val condition = DeviceEventCondition(
                                id = UUID.randomUUID().toString(),
                                eventType = DeviceEventType.GPS_STATE,
                                gpsStateType = selectedGpsStateType
                            )
                            onConfigurationComplete(condition)
                        }
                    ) {
                        Text(
                            text = "确认",
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 描述
            Text(
                text = "当GPS状态改变时触发条件",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "选择GPS状态类型",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )

            // GPS状态类型选择
            Column(
                modifier = Modifier.selectableGroup(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                GpsStateType.values().forEach { gpsStateType ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedGpsStateType == gpsStateType,
                                onClick = { selectedGpsStateType = gpsStateType },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 4.dp)
                    ) {
                        RadioButton(
                            selected = selectedGpsStateType == gpsStateType,
                            onClick = { selectedGpsStateType = gpsStateType }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = gpsStateType.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}
